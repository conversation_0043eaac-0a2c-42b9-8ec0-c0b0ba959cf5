# Admin-Only User Management Endpoints

This document describes the admin-only endpoints for user management in the Django REST Framework API.

## 🔐 Authentication & Permissions

All endpoints require:
- **JWT Authentication**: Include `Authorization: Bearer <access_token>` header
- **Admin Role**: Only users with `user_type = 'admin'` can access these endpoints

## 📡 API Endpoints

### Base URL: `/auth/admin/`

---

## 1. User Management (All Users)

### List All Users
```http
GET /auth/admin/users/
```

**Query Parameters:**
- `role` - Filter by user type: `admin`, `collaborator`, `client`
- `is_active` - Filter by active status: `true`, `false`
- `isActive` - Filter collaborators by isActive status: `true`, `false`
- `isVerified` - Filter clients by isVerified status: `true`, `false`
- `search` - Search in firstName, lastName, phone
- `ordering` - Order by: `date_joined`, `firstName`, `lastName` (prefix with `-` for descending)

**Examples:**
```bash
# Get all users
GET /auth/admin/users/

# Get only collaborators
GET /auth/admin/users/?role=collaborator

# Get active collaborators
GET /auth/admin/users/?role=collaborator&isActive=true

# Get verified clients
GET /auth/admin/users/?role=client&isVerified=true

# Search users
GET /auth/admin/users/?search=John

# Order by name
GET /auth/admin/users/?ordering=firstName
```

**Response:**
```json
{
    "count": 10,
    "next": null,
    "previous": null,
    "results": [
        {
            "id": 1,
            "firstName": "Admin",
            "lastName": "User",
            "full_name": "Admin User",
            "phone": "+1234567890",
            "user_type": "admin",
            "is_active": true,
            "date_joined": "2025-07-25T10:00:00Z",
            "last_login": "2025-07-25T12:00:00Z",
            "isActive": null,
            "isVerified": null
        }
    ]
}
```

### Get User Details
```http
GET /auth/admin/users/{id}/
```

### User Actions (Block/Unblock, Verify/Unverify)
```http
PATCH /auth/admin/users/{id}/actions/
```

**Request Body:**
```json
{
    "action": "block|unblock|verify|unverify"
}
```

**Actions:**
- `block` - Block a collaborator (set isActive = false)
- `unblock` - Unblock a collaborator (set isActive = true)
- `verify` - Verify a client (set isVerified = true)
- `unverify` - Unverify a client (set isVerified = false)

**Examples:**
```bash
# Block a collaborator
PATCH /auth/admin/users/5/actions/
{
    "action": "block"
}

# Verify a client
PATCH /auth/admin/users/10/actions/
{
    "action": "verify"
}
```

---

## 2. Collaborator Management (CRUD)

### List Collaborators
```http
GET /auth/admin/collaborators/
```

**Query Parameters:**
- `isActive` - Filter by active status: `true`, `false`
- `is_active` - Filter by Django's is_active field: `true`, `false`
- `search` - Search in firstName, lastName, phone
- `ordering` - Order by: `date_joined`, `firstName`, `lastName`

### Create Collaborator
```http
POST /auth/admin/collaborators/
```

**Request Body:**
```json
{
    "firstName": "Jane",
    "lastName": "Smith",
    "phone": "+1234567892",
    "password": "securepass123",
    "password_confirm": "securepass123",
    "isActive": true
}
```

### Get Collaborator Details
```http
GET /auth/admin/collaborators/{id}/
```

### Update Collaborator
```http
PUT /auth/admin/collaborators/{id}/
PATCH /auth/admin/collaborators/{id}/
```

**Request Body (PATCH example):**
```json
{
    "firstName": "Jane Updated",
    "isActive": false
}
```

### Delete Collaborator
```http
DELETE /auth/admin/collaborators/{id}/
```

### Toggle Collaborator Active Status
```http
PATCH /auth/admin/collaborators/{id}/toggle-active/
```

**Response:**
```json
{
    "message": "Collaborator Jane Smith has been deactivated.",
    "collaborator": {
        "id": 5,
        "firstName": "Jane",
        "lastName": "Smith",
        "full_name": "Jane Smith",
        "phone": "+1234567892",
        "user_type": "collaborator",
        "is_active": true,
        "isActive": false,
        "date_joined": "2025-07-25T10:00:00Z",
        "last_login": null
    }
}
```

---

## 🧪 Testing Examples

### 1. Login as Admin
```bash
curl -X POST http://127.0.0.1:8000/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"phone": "+1234567890", "password": "admin123"}'
```

### 2. List All Users
```bash
curl -X GET http://127.0.0.1:8000/auth/admin/users/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 3. Filter Collaborators
```bash
curl -X GET "http://127.0.0.1:8000/auth/admin/users/?role=collaborator&isActive=true" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 4. Create Collaborator
```bash
curl -X POST http://127.0.0.1:8000/auth/admin/collaborators/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "New",
    "lastName": "Collaborator",
    "phone": "+1234567893",
    "password": "newpass123",
    "password_confirm": "newpass123",
    "isActive": true
  }'
```

### 5. Block a Collaborator
```bash
curl -X PATCH http://127.0.0.1:8000/auth/admin/users/5/actions/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"action": "block"}'
```

### 6. Verify a Client
```bash
curl -X PATCH http://127.0.0.1:8000/auth/admin/users/10/actions/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"action": "verify"}'
```

---

## 🔒 Security Features

- **Admin-Only Access**: All endpoints require admin role
- **JWT Authentication**: Secure token-based authentication
- **Input Validation**: Comprehensive validation for all inputs
- **Permission Checks**: Role-based access control
- **Password Security**: Password validation and hashing

---

## 📝 Response Codes

- `200 OK` - Successful operation
- `201 Created` - Resource created successfully
- `400 Bad Request` - Invalid input data
- `401 Unauthorized` - Missing or invalid authentication
- `403 Forbidden` - Insufficient permissions (not admin)
- `404 Not Found` - Resource not found
- `500 Internal Server Error` - Server error

---

## 🎯 Features Implemented

✅ **User Listing**: List all users with filtering and search  
✅ **Role Filtering**: Filter users by admin/collaborator/client  
✅ **Collaborator CRUD**: Full create, read, update, delete for collaborators  
✅ **User Actions**: Block/unblock collaborators, verify/unverify clients  
✅ **Search & Filtering**: Search by name/phone, filter by status  
✅ **Pagination**: Built-in pagination for large datasets  
✅ **Permission Control**: Admin-only access with proper validation  
✅ **RESTful Design**: Following REST conventions for all endpoints
