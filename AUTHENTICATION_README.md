# Django Authentication System

A comprehensive Django-based authentication system with role-based user management using JWT tokens.

## 🏗️ Architecture

### User Model Hierarchy
```
User (Base Model)
├── Admin (Proxy Model)
├── Collaborator (with isActive field)
└── Client (with isVerified field)
```

### Fields
- **Base User**: `id`, `firstName`, `lastName`, `phone`, `password` (hashed), `user_type`
- **Admin**: No additional fields (proxy model)
- **Collaborator**: `isActive` (Boolean)
- **Client**: `isVerified` (Boolean)

## 🔐 Authentication Features

- **Phone-based authentication** (no username/email required)
- **JWT token authentication** with access and refresh tokens
- **Role-based user system** (Admin, Collaborator, Client)
- **Global login endpoint** for all user types
- **Client-only registration** endpoint
- **Unique phone number validation**
- **Custom user manager** for phone-based user creation

## 📡 API Endpoints

### Authentication Endpoints

#### 1. Login (Global)
```http
POST /auth/login/
Content-Type: application/json

{
    "phone": "+1234567890",
    "password": "your_password"
}
```

**Response (200 OK):**
```json
{
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
        "id": 1,
        "firstName": "John",
        "lastName": "Doe",
        "phone": "+1234567890",
        "user_type": "client",
        "date_joined": "2025-07-25T13:48:21.424523Z"
    },
    "message": "Login successful. Welcome John Doe!"
}
```

#### 2. Register (Client Only)
```http
POST /auth/register/
Content-Type: application/json

{
    "firstName": "John",
    "lastName": "Doe",
    "phone": "+1234567891",
    "password": "secure_password123",
    "password_confirm": "secure_password123"
}
```

**Response (201 Created):**
```json
{
    "user": {
        "id": 2,
        "firstName": "John",
        "lastName": "Doe",
        "phone": "+1234567891",
        "user_type": "client",
        "date_joined": "2025-07-25T13:49:51.830542Z"
    },
    "message": "Registration successful. Welcome John Doe!"
}
```

## 🛠️ Setup Instructions

### 1. Install Dependencies
```bash
pip install djangorestframework djangorestframework-simplejwt
```

### 2. Add to INSTALLED_APPS
```python
INSTALLED_APPS = [
    # ... other apps
    'rest_framework',
    'rest_framework_simplejwt',
    'authentication',
]
```

### 3. Configure Settings
```python
# Custom User Model
AUTH_USER_MODEL = 'authentication.User'

# Django REST Framework
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
}

# JWT Configuration
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=60),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
    'UPDATE_LAST_LOGIN': True,
}
```

### 4. Run Migrations
```bash
python manage.py makemigrations
python manage.py migrate
```

### 5. Create Default Admin User
```bash
python manage.py create_admin
```

Default admin credentials:
- Phone: `+1234567890`
- Password: `admin123`

## 🧪 Testing

### Manual Testing with cURL

1. **Register a new client:**
```bash
curl -X POST http://127.0.0.1:8001/auth/register/ \
  -H "Content-Type: application/json" \
  -d '{"firstName": "John", "lastName": "Doe", "phone": "+1234567891", "password": "testpass123", "password_confirm": "testpass123"}'
```

2. **Login as client:**
```bash
curl -X POST http://127.0.0.1:8001/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"phone": "+1234567891", "password": "testpass123"}'
```

3. **Login as admin:**
```bash
curl -X POST http://127.0.0.1:8001/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"phone": "+1234567890", "password": "admin123"}'
```

## 🔧 Management Commands

### Create Admin User
```bash
python manage.py create_admin [options]

Options:
  --phone PHONE         Phone number (default: +1234567890)
  --password PASSWORD   Password (default: admin123)
  --first-name NAME     First name (default: Admin)
  --last-name NAME      Last name (default: User)
```

## 🎯 Key Features Implemented

✅ **Custom User Model**: Phone-based authentication with AbstractBaseUser  
✅ **Role Inheritance**: Admin, Collaborator, Client models with specific fields  
✅ **JWT Authentication**: Access and refresh tokens with proper configuration  
✅ **Global Login**: Single endpoint for all user types  
✅ **Client Registration**: Dedicated endpoint for new client registration  
✅ **Phone Validation**: Unique phone numbers with regex validation  
✅ **Management Command**: Easy admin user creation  
✅ **Django Admin**: Proper admin interface for user management  
✅ **Error Handling**: Comprehensive validation and error responses  

## 🔒 Security Features

- **Password Hashing**: Django's built-in password hashing
- **JWT Tokens**: Secure token-based authentication
- **Token Rotation**: Refresh tokens are rotated on use
- **Phone Validation**: Regex validation for phone numbers
- **Unique Constraints**: Phone numbers must be unique
- **Permission Classes**: Configurable permission system

## 📝 Notes

- Default user type for registration is `client`
- Admin and Collaborator users must be created through Django admin or management commands
- Phone numbers are validated with regex pattern: `^\+?1?\d{9,15}$`
- JWT tokens expire after 60 minutes (configurable)
- Refresh tokens expire after 7 days (configurable)
