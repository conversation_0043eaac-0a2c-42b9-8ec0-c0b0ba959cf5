from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import User, Admin, Collaborator, Client


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    list_display = ('phone', 'firstName', 'lastName', 'user_type', 'is_active', 'date_joined')
    list_filter = ('user_type', 'is_active', 'is_staff', 'date_joined')
    search_fields = ('phone', 'firstName', 'lastName')
    ordering = ('date_joined',)

    fieldsets = (
        (None, {'fields': ('phone', 'password')}),
        ('Personal info', {'fields': ('firstName', 'lastName', 'user_type')}),
        ('Permissions', {'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        ('Important dates', {'fields': ('last_login', 'date_joined')}),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('phone', 'firstName', 'lastName', 'password1', 'password2', 'user_type'),
        }),
    )


@admin.register(Admin)
class AdminAdmin(UserAdmin):
    list_display = ('phone', 'firstName', 'lastName', 'is_active', 'date_joined')

    def get_queryset(self, request):
        return super().get_queryset(request).filter(user_type='admin')


@admin.register(Collaborator)
class CollaboratorAdmin(UserAdmin):
    list_display = ('phone', 'firstName', 'lastName', 'isActive', 'is_active', 'date_joined')

    fieldsets = UserAdmin.fieldsets + (
        ('Collaborator Info', {'fields': ('isActive',)}),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).filter(user_type='collaborator')


@admin.register(Client)
class ClientAdmin(UserAdmin):
    list_display = ('phone', 'firstName', 'lastName', 'isVerified', 'is_active', 'date_joined')

    fieldsets = UserAdmin.fieldsets + (
        ('Client Info', {'fields': ('isVerified',)}),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).filter(user_type='client')
