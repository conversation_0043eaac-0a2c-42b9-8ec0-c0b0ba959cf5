from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import admin_views

# Create a router for admin ViewSets
admin_router = DefaultRouter()
admin_router.register(r'users', admin_views.AdminUserManagementViewSet, basename='admin-users')
admin_router.register(r'collaborators', admin_views.AdminCollaboratorViewSet, basename='admin-collaborators')

app_name = 'admin'

urlpatterns = [
    # Include all router URLs
    path('', include(admin_router.urls)),
]
