from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.db.models import Q

from .models import User, Collaborator, Client
from .permissions import IsAdminUser
from .serializers import (
    AdminUserListSerializer,
    CollaboratorCreateSerializer,
    CollaboratorUpdateSerializer,
    CollaboratorDetailSerializer,
    UserActionSerializer
)


class AdminUserManagementViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Admin-only ViewSet for managing all users
    Provides list and retrieve functionality with filtering
    """
    queryset = User.objects.all().order_by('-date_joined')
    serializer_class = AdminUserListSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['user_type', 'is_active']
    search_fields = ['firstName', 'lastName', 'phone']
    ordering_fields = ['date_joined', 'firstName', 'lastName']
    ordering = ['-date_joined']

    def get_queryset(self):
        """
        Filter queryset based on query parameters
        """
        queryset = super().get_queryset()
        
        # Filter by user role
        role = self.request.query_params.get('role', None)
        if role in ['admin', 'collaborator', 'client']:
            queryset = queryset.filter(user_type=role)
        
        # Filter by active status for collaborators
        if role == 'collaborator':
            is_active = self.request.query_params.get('isActive', None)
            if is_active is not None:
                collaborator_ids = Collaborator.objects.filter(
                    isActive=is_active.lower() == 'true'
                ).values_list('id', flat=True)
                queryset = queryset.filter(id__in=collaborator_ids)
        
        # Filter by verified status for clients
        if role == 'client':
            is_verified = self.request.query_params.get('isVerified', None)
            if is_verified is not None:
                client_ids = Client.objects.filter(
                    isVerified=is_verified.lower() == 'true'
                ).values_list('id', flat=True)
                queryset = queryset.filter(id__in=client_ids)
        
        return queryset

    @action(detail=True, methods=['patch'], url_path='actions')
    def user_actions(self, request, pk=None):
        """
        Perform actions on users (block/unblock collaborators, verify/unverify clients)
        """
        user = self.get_object()
        serializer = UserActionSerializer(data=request.data, context={'user': user})
        
        if serializer.is_valid():
            action = serializer.validated_data['action']
            
            try:
                if action == 'block':
                    collaborator = Collaborator.objects.get(id=user.id)
                    collaborator.isActive = False
                    collaborator.save()
                    message = f"Collaborator {user.full_name} has been blocked."
                
                elif action == 'unblock':
                    collaborator = Collaborator.objects.get(id=user.id)
                    collaborator.isActive = True
                    collaborator.save()
                    message = f"Collaborator {user.full_name} has been unblocked."
                
                elif action == 'verify':
                    client = Client.objects.get(id=user.id)
                    client.isVerified = True
                    client.save()
                    message = f"Client {user.full_name} has been verified."
                
                elif action == 'unverify':
                    client = Client.objects.get(id=user.id)
                    client.isVerified = False
                    client.save()
                    message = f"Client {user.full_name} has been unverified."
                
                return Response({
                    'message': message,
                    'user': AdminUserListSerializer(user).data
                }, status=status.HTTP_200_OK)
                
            except (Collaborator.DoesNotExist, Client.DoesNotExist):
                return Response({
                    'error': 'User type mismatch or user not found.'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AdminCollaboratorViewSet(viewsets.ModelViewSet):
    """
    Admin-only ViewSet for CRUD operations on Collaborators
    """
    queryset = Collaborator.objects.all().order_by('-date_joined')
    permission_classes = [IsAuthenticated, IsAdminUser]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['isActive', 'is_active']
    search_fields = ['firstName', 'lastName', 'phone']
    ordering_fields = ['date_joined', 'firstName', 'lastName']
    ordering = ['-date_joined']

    def get_serializer_class(self):
        """
        Return appropriate serializer based on action
        """
        if self.action == 'create':
            return CollaboratorCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return CollaboratorUpdateSerializer
        else:
            return CollaboratorDetailSerializer

    def perform_create(self, serializer):
        """
        Create a new collaborator
        """
        collaborator = serializer.save()
        return collaborator

    def perform_update(self, serializer):
        """
        Update an existing collaborator
        """
        collaborator = serializer.save()
        return collaborator

    @action(detail=True, methods=['patch'], url_path='toggle-active')
    def toggle_active(self, request, pk=None):
        """
        Toggle the isActive status of a collaborator
        """
        collaborator = self.get_object()
        collaborator.isActive = not collaborator.isActive
        collaborator.save()
        
        status_text = "activated" if collaborator.isActive else "deactivated"
        
        return Response({
            'message': f"Collaborator {collaborator.full_name} has been {status_text}.",
            'collaborator': CollaboratorDetailSerializer(collaborator).data
        }, status=status.HTTP_200_OK)
