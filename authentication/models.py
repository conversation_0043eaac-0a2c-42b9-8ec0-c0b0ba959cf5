from django.contrib.auth.models import AbstractBaseUser, BaseUserManager, PermissionsMixin
from django.db import models
from django.core.validators import RegexValidator


class CustomUserManager(BaseUserManager):
    """
    Custom user manager for phone-based authentication
    """
    def create_user(self, phone, password=None, **extra_fields):
        """
        Create and return a regular user with phone and password
        """
        if not phone:
            raise ValueError('The Phone field must be set')

        user = self.model(phone=phone, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, phone, password=None, **extra_fields):
        """
        Create and return a superuser with phone and password
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('user_type', 'admin')

        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')

        return self.create_user(phone, password, **extra_fields)


class User(AbstractBaseUser, PermissionsMixin):
    """
    Base User model with phone-based authentication
    """
    USER_TYPE_CHOICES = [
        ('admin', 'Admin'),
        ('collaborator', 'Collaborator'),
        ('client', 'Client'),
    ]

    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
    )

    id = models.AutoField(primary_key=True)
    firstName = models.CharField(max_length=30)
    lastName = models.CharField(max_length=30)
    phone = models.CharField(validators=[phone_regex], max_length=17, unique=True)
    user_type = models.CharField(max_length=20, choices=USER_TYPE_CHOICES, default='client')

    # Required fields for AbstractBaseUser
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)
    date_joined = models.DateTimeField(auto_now_add=True)

    objects = CustomUserManager()

    USERNAME_FIELD = 'phone'
    REQUIRED_FIELDS = ['firstName', 'lastName']

    class Meta:
        db_table = 'auth_user'

    def __str__(self):
        return f"{self.firstName} {self.lastName} ({self.phone})"

    @property
    def full_name(self):
        return f"{self.firstName} {self.lastName}"


class Admin(User):
    """
    Admin user model - inherits from User with no additional fields
    """
    class Meta:
        proxy = True
        verbose_name = 'Admin'
        verbose_name_plural = 'Admins'

    def save(self, *args, **kwargs):
        self.user_type = 'admin'
        self.is_staff = True
        super().save(*args, **kwargs)


class Collaborator(User):
    """
    Collaborator user model - inherits from User with isActive field
    """
    isActive = models.BooleanField(default=True)

    class Meta:
        verbose_name = 'Collaborator'
        verbose_name_plural = 'Collaborators'

    def save(self, *args, **kwargs):
        self.user_type = 'collaborator'
        super().save(*args, **kwargs)


class Client(User):
    """
    Client user model - inherits from User with isVerified field
    """
    isVerified = models.BooleanField(default=False)

    class Meta:
        verbose_name = 'Client'
        verbose_name_plural = 'Clients'

    def save(self, *args, **kwargs):
        self.user_type = 'client'
        super().save(*args, **kwargs)
