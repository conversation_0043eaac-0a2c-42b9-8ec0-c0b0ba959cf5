from rest_framework.permissions import BasePermission


class IsAdminUser(BasePermission):
    """
    Custom permission to only allow admin users to access the view.
    """
    
    def has_permission(self, request, view):
        """
        Check if the user is authenticated and is an admin
        """
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.user_type == 'admin'
        )


class IsAdminOrReadOnly(BasePermission):
    """
    Custom permission to allow read-only access to authenticated users,
    but only allow write access to admin users.
    """
    
    def has_permission(self, request, view):
        """
        Check permissions based on request method
        """
        if request.method in ['GET', 'HEAD', 'OPTIONS']:
            return request.user and request.user.is_authenticated
        
        return (
            request.user and 
            request.user.is_authenticated and 
            request.user.user_type == 'admin'
        )


class IsOwnerOrAdmin(BasePermission):
    """
    Custom permission to allow users to access their own data,
    or allow admin users to access any data.
    """
    
    def has_permission(self, request, view):
        """
        Check if user is authenticated
        """
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        """
        Check if user is the owner of the object or is an admin
        """
        return (
            obj == request.user or 
            request.user.user_type == 'admin'
        )
