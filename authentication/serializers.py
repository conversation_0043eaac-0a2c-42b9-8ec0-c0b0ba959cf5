from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from .models import User, Client, Collaborator, Admin


class UserSerializer(serializers.ModelSerializer):
    """
    Serializer for User model
    """
    class Meta:
        model = User
        fields = ['id', 'firstName', 'lastName', 'phone', 'user_type', 'date_joined']
        read_only_fields = ['id', 'date_joined']


class LoginSerializer(serializers.Serializer):
    """
    Serializer for user login
    """
    phone = serializers.CharField(max_length=17)
    password = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        phone = attrs.get('phone')
        password = attrs.get('password')
        
        if phone and password:
            user = authenticate(request=self.context.get('request'),
                              username=phone, password=password)
            
            if not user:
                raise serializers.ValidationError(
                    'Unable to log in with provided credentials.',
                    code='authorization'
                )
            
            if not user.is_active:
                raise serializers.ValidationError(
                    'User account is disabled.',
                    code='authorization'
                )
            
            attrs['user'] = user
            return attrs
        else:
            raise serializers.ValidationError(
                'Must include "phone" and "password".',
                code='authorization'
            )


class RegisterSerializer(serializers.ModelSerializer):
    """
    Serializer for client registration
    """
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = Client
        fields = ['firstName', 'lastName', 'phone', 'password', 'password_confirm']
    
    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match.")
        return attrs
    
    def validate_phone(self, value):
        if User.objects.filter(phone=value).exists():
            raise serializers.ValidationError("A user with this phone number already exists.")
        return value
    
    def create(self, validated_data):
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        
        # Create a Client instance
        client = Client.objects.create_user(
            password=password,
            **validated_data
        )
        return client


class LoginResponseSerializer(serializers.Serializer):
    """
    Serializer for login response
    """
    access_token = serializers.CharField()
    refresh_token = serializers.CharField()
    user = UserSerializer()
    message = serializers.CharField()


class RegisterResponseSerializer(serializers.Serializer):
    """
    Serializer for registration response
    """
    user = UserSerializer()
    message = serializers.CharField()


# ============================================================================
# ADMIN USER MANAGEMENT SERIALIZERS
# ============================================================================

class AdminUserListSerializer(serializers.ModelSerializer):
    """
    Serializer for listing users (read-only) with all relevant fields
    """
    full_name = serializers.ReadOnlyField()
    isActive = serializers.SerializerMethodField()
    isVerified = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            'id', 'firstName', 'lastName', 'full_name', 'phone',
            'user_type', 'is_active', 'date_joined', 'last_login',
            'isActive', 'isVerified'
        ]
        read_only_fields = ['id', 'date_joined', 'last_login']

    def get_isActive(self, obj):
        """Get isActive field for Collaborators, None for others"""
        if obj.user_type == 'collaborator':
            try:
                collaborator = Collaborator.objects.get(id=obj.id)
                return collaborator.isActive
            except Collaborator.DoesNotExist:
                return None
        return None

    def get_isVerified(self, obj):
        """Get isVerified field for Clients, None for others"""
        if obj.user_type == 'client':
            try:
                client = Client.objects.get(id=obj.id)
                return client.isVerified
            except Client.DoesNotExist:
                return None
        return None


class CollaboratorCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating Collaborators
    """
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)

    class Meta:
        model = Collaborator
        fields = ['firstName', 'lastName', 'phone', 'password', 'password_confirm', 'isActive']

    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match.")
        return attrs

    def validate_phone(self, value):
        if User.objects.filter(phone=value).exists():
            raise serializers.ValidationError("A user with this phone number already exists.")
        return value

    def create(self, validated_data):
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')

        collaborator = Collaborator.objects.create_user(
            password=password,
            **validated_data
        )
        return collaborator


class CollaboratorUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating Collaborators
    """
    class Meta:
        model = Collaborator
        fields = ['firstName', 'lastName', 'phone', 'isActive']

    def validate_phone(self, value):
        # Allow current phone number, but check for duplicates with other users
        if self.instance and self.instance.phone == value:
            return value
        if User.objects.filter(phone=value).exists():
            raise serializers.ValidationError("A user with this phone number already exists.")
        return value


class CollaboratorDetailSerializer(serializers.ModelSerializer):
    """
    Serializer for Collaborator details (read-only)
    """
    full_name = serializers.ReadOnlyField()

    class Meta:
        model = Collaborator
        fields = [
            'id', 'firstName', 'lastName', 'full_name', 'phone',
            'user_type', 'is_active', 'isActive', 'date_joined', 'last_login'
        ]
        read_only_fields = ['id', 'user_type', 'date_joined', 'last_login']


class UserActionSerializer(serializers.Serializer):
    """
    Serializer for user actions (block/unblock, verify/unverify)
    """
    action = serializers.ChoiceField(choices=[
        ('block', 'Block User'),
        ('unblock', 'Unblock User'),
        ('verify', 'Verify User'),
        ('unverify', 'Unverify User'),
    ])

    def validate(self, attrs):
        action = attrs.get('action')
        user = self.context.get('user')

        if not user:
            raise serializers.ValidationError("User not found.")

        # Validate action based on user type
        if action in ['block', 'unblock'] and user.user_type != 'collaborator':
            raise serializers.ValidationError("Block/unblock actions are only available for collaborators.")

        if action in ['verify', 'unverify'] and user.user_type != 'client':
            raise serializers.ValidationError("Verify/unverify actions are only available for clients.")

        return attrs
