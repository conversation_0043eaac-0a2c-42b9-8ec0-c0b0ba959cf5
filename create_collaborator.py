#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a collaborator user for testing
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sademy_api.settings')
django.setup()

from authentication.models import Collaborator

# Create a collaborator user
collaborator = Collaborator.objects.create_user(
    phone="+1234567892",
    password="collab123",
    firstName="Jane",
    lastName="Smith",
    isActive=True
)

print(f"Successfully created collaborator: {collaborator.full_name} ({collaborator.phone})")
print(f"User Type: {collaborator.user_type}")
print(f"Is Active: {collaborator.isActive}")
