#!/usr/bin/env python3
"""
Test script for admin-only user management endpoints
"""
import os
import django
import json
import requests

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sademy_api.settings')
django.setup()

from authentication.models import User, Admin, Collaborator, Client

# Base URL for the API
BASE_URL = "http://127.0.0.1:8001"

def get_admin_token():
    """Get JWT token for admin user"""
    login_data = {
        "phone": "+1234567890",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login/", json=login_data)
    if response.status_code == 200:
        return response.json()['access_token']
    else:
        print(f"Failed to login admin: {response.text}")
        return None

def test_admin_endpoints():
    """Test all admin endpoints"""
    print("🔐 Testing Admin-Only User Management Endpoints")
    print("=" * 60)
    
    # Get admin token
    token = get_admin_token()
    if not token:
        print("❌ Failed to get admin token. Make sure admin user exists and server is running.")
        return
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print("✅ Admin authentication successful")
    print()
    
    # Test 1: List all users
    print("📋 Test 1: List All Users")
    response = requests.get(f"{BASE_URL}/auth/admin/users/", headers=headers)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        users = response.json()['results']
        print(f"Found {len(users)} users")
        for user in users[:3]:  # Show first 3 users
            print(f"  - {user['full_name']} ({user['phone']}) - {user['user_type']}")
    else:
        print(f"Error: {response.text}")
    print()
    
    # Test 2: Filter users by role
    print("🔍 Test 2: Filter Users by Role")
    for role in ['admin', 'collaborator', 'client']:
        response = requests.get(f"{BASE_URL}/auth/admin/users/?role={role}", headers=headers)
        print(f"  {role.capitalize()}s: {response.status_code}")
        if response.status_code == 200:
            count = response.json()['count']
            print(f"    Found {count} {role}s")
    print()
    
    # Test 3: Search users
    print("🔎 Test 3: Search Users")
    response = requests.get(f"{BASE_URL}/auth/admin/users/?search=Admin", headers=headers)
    print(f"Search for 'Admin': {response.status_code}")
    if response.status_code == 200:
        count = response.json()['count']
        print(f"  Found {count} matching users")
    print()
    
    # Test 4: Create a new collaborator
    print("➕ Test 4: Create New Collaborator")
    collaborator_data = {
        "firstName": "Test",
        "lastName": "Collaborator",
        "phone": "+1234567999",
        "password": "testpass123",
        "password_confirm": "testpass123",
        "isActive": True
    }
    
    response = requests.post(f"{BASE_URL}/auth/admin/collaborators/", 
                           json=collaborator_data, headers=headers)
    print(f"Create collaborator: {response.status_code}")
    if response.status_code == 201:
        collaborator = response.json()
        print(f"  Created: {collaborator.get('full_name', collaborator.get('firstName', 'Unknown'))} {collaborator.get('lastName', '')} (ID: {collaborator['id']})")
        test_collaborator_id = collaborator['id']
    else:
        print(f"  Error: {response.text}")
        # Try to find existing test collaborator
        response = requests.get(f"{BASE_URL}/auth/admin/users/?search=Test", headers=headers)
        if response.status_code == 200:
            users = response.json()['results']
            test_collaborator = next((u for u in users if u['user_type'] == 'collaborator'), None)
            if test_collaborator:
                test_collaborator_id = test_collaborator['id']
                print(f"  Using existing test collaborator (ID: {test_collaborator_id})")
            else:
                test_collaborator_id = None
        else:
            test_collaborator_id = None
    print()
    
    # Test 5: List collaborators
    print("👥 Test 5: List Collaborators")
    response = requests.get(f"{BASE_URL}/auth/admin/collaborators/", headers=headers)
    print(f"List collaborators: {response.status_code}")
    if response.status_code == 200:
        collaborators = response.json()['results']
        print(f"  Found {len(collaborators)} collaborators")
        for collab in collaborators[:2]:
            print(f"    - {collab['full_name']} (Active: {collab['isActive']})")
    print()
    
    # Test 6: User actions (block/unblock collaborator)
    if test_collaborator_id:
        print("🔒 Test 6: Block/Unblock Collaborator")
        
        # Block collaborator
        action_data = {"action": "block"}
        response = requests.patch(f"{BASE_URL}/auth/admin/users/{test_collaborator_id}/actions/", 
                                json=action_data, headers=headers)
        print(f"  Block collaborator: {response.status_code}")
        if response.status_code == 200:
            print(f"    {response.json()['message']}")
        
        # Unblock collaborator
        action_data = {"action": "unblock"}
        response = requests.patch(f"{BASE_URL}/auth/admin/users/{test_collaborator_id}/actions/", 
                                json=action_data, headers=headers)
        print(f"  Unblock collaborator: {response.status_code}")
        if response.status_code == 200:
            print(f"    {response.json()['message']}")
    print()
    
    # Test 7: Test permissions (try with non-admin user)
    print("🚫 Test 7: Test Permission Restrictions")
    
    # Try to create a client user first for testing
    client_data = {
        "firstName": "Test",
        "lastName": "Client",
        "phone": "+1234567998",
        "password": "testpass123",
        "password_confirm": "testpass123"
    }
    
    # Register client
    response = requests.post(f"{BASE_URL}/auth/register/", json=client_data)
    if response.status_code == 201:
        print("  Created test client for permission testing")
        
        # Login as client
        login_data = {"phone": "+1234567998", "password": "testpass123"}
        response = requests.post(f"{BASE_URL}/auth/login/", json=login_data)
        if response.status_code == 200:
            client_token = response.json()['access_token']
            client_headers = {"Authorization": f"Bearer {client_token}"}
            
            # Try to access admin endpoint as client
            response = requests.get(f"{BASE_URL}/auth/admin/users/", headers=client_headers)
            print(f"  Client accessing admin endpoint: {response.status_code}")
            if response.status_code == 403:
                print("    ✅ Correctly blocked non-admin access")
            else:
                print("    ❌ Permission check failed!")
    
    print()
    print("🎉 Admin endpoint testing completed!")

if __name__ == "__main__":
    test_admin_endpoints()
