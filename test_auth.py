#!/usr/bin/env python3
"""
Test script for the authentication system
"""
import requests
import json

BASE_URL = "http://127.0.0.1:8001"

def test_register():
    """Test client registration"""
    print("Testing client registration...")
    
    data = {
        "firstName": "<PERSON>",
        "lastName": "Doe",
        "phone": "+1234567891",
        "password": "testpass123",
        "password_confirm": "testpass123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/register/", json=data)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print("-" * 50)
    
    return response.status_code == 201

def test_login_client():
    """Test client login"""
    print("Testing client login...")
    
    data = {
        "phone": "+1234567891",
        "password": "testpass123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login/", json=data)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print("-" * 50)
    
    return response.status_code == 200

def test_login_admin():
    """Test admin login"""
    print("Testing admin login...")
    
    data = {
        "phone": "+1234567890",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login/", json=data)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print("-" * 50)
    
    return response.status_code == 200

def test_invalid_login():
    """Test invalid login"""
    print("Testing invalid login...")
    
    data = {
        "phone": "+9999999999",
        "password": "wrongpassword"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login/", json=data)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print("-" * 50)
    
    return response.status_code == 400

if __name__ == "__main__":
    print("🚀 Testing Authentication System")
    print("=" * 50)
    
    # Test registration
    register_success = test_register()
    
    # Test client login
    client_login_success = test_login_client()
    
    # Test admin login
    admin_login_success = test_login_admin()
    
    # Test invalid login
    invalid_login_success = test_invalid_login()
    
    print("📊 Test Results:")
    print(f"✅ Client Registration: {'PASS' if register_success else 'FAIL'}")
    print(f"✅ Client Login: {'PASS' if client_login_success else 'FAIL'}")
    print(f"✅ Admin Login: {'PASS' if admin_login_success else 'FAIL'}")
    print(f"✅ Invalid Login Rejection: {'PASS' if invalid_login_success else 'FAIL'}")
    
    all_tests_passed = all([register_success, client_login_success, admin_login_success, invalid_login_success])
    print(f"\n🎯 Overall Result: {'ALL TESTS PASSED! 🎉' if all_tests_passed else 'SOME TESTS FAILED ❌'}")
